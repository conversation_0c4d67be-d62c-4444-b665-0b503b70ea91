<template>
  <div class="data-table-page">
    <h2 class="page-title">数据管理</h2>
    
    <!-- 搜索和操作区域 -->
    <!-- <el-card class="search-card">
      <el-row :gutter="24">
        <el-col :xs="24" :sm="12" :md="8">
          <el-input
            v-model="searchForm.keyword"
            placeholder="请输入关键词搜索"
            clearable
            @clear="handleSearch"
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
            style="width: 100%"
          >
            <el-option label="全部" value="" />
            <el-option label="启用" value="1" />
            <el-option label="禁用" value="0" />
          </el-select>
        </el-col>
        <el-col :xs="24" :sm="24" :md="8">
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
          <el-button type="success" @click="handleAdd">
            <el-icon><Plus /></el-icon>
            新增
          </el-button>
        </el-col>
      </el-row>
    </el-card> -->

    <!-- 数据表格 -->
    <!-- <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="name" label="名称" min-width="120" />
        <el-table-column prop="email" label="邮箱" min-width="180" />
        <el-table-column prop="phone" label="手机号" width="120" />
        <el-table-column prop="role" label="角色" width="100">
          <template #default="{ row }">
            <el-tag :type="row.role === 'admin' ? 'danger' : 'primary'">
              {{ row.role === 'admin' ? '管理员' : '用户' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-switch
              v-model="row.status"
              :active-value="1"
              :inactive-value="0"
              @change="handleStatusChange(row)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="180" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button type="danger" size="small" @click="handleDelete(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

     
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card> -->

    <!-- 编辑弹窗 -->
    <!-- <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="80px"
      >
        <el-form-item label="姓名" prop="name">
          <el-input v-model="form.name" placeholder="请输入姓名" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="form.email" placeholder="请输入邮箱" />
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="form.phone" placeholder="请输入手机号" />
        </el-form-item>
        <el-form-item label="角色" prop="role">
          <el-select v-model="form.role" placeholder="请选择角色" style="width: 100%">
            <el-option label="用户" value="user" />
            <el-option label="管理员" value="admin" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
          确定
        </el-button>
      </template>
    </el-dialog> -->
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 搜索表单
const searchForm = reactive({
  keyword: '',
  status: ''
})

// 表格数据
const loading = ref(false)
const tableData = ref([])
const selectedRows = ref([])

// 分页
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 弹窗
const dialogVisible = ref(false)
const dialogTitle = ref('')
const submitLoading = ref(false)
const formRef = ref(null)

// 表单数据
const form = reactive({
  id: null,
  name: '',
  email: '',
  phone: '',
  role: 'user'
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  role: [
    { required: true, message: '请选择角色', trigger: 'change' }
  ]
}

// 模拟数据
const mockData = [
  {
    id: 1,
    name: '张三',
    email: '<EMAIL>',
    phone: '13812345678',
    role: 'admin',
    status: 1,
    createTime: '2024-01-15 10:30:00'
  },
  {
    id: 2,
    name: '李四',
    email: '<EMAIL>',
    phone: '13987654321',
    role: 'user',
    status: 1,
    createTime: '2024-01-16 14:20:00'
  },
  {
    id: 3,
    name: '王五',
    email: '<EMAIL>',
    phone: '13655667788',
    role: 'user',
    status: 0,
    createTime: '2024-01-17 09:15:00'
  }
]

// 获取表格数据
const getTableData = async () => {
  loading.value = true
  try {
    // 模拟API请求
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    tableData.value = mockData
    pagination.total = mockData.length
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.currentPage = 1
  getTableData()
}

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    keyword: '',
    status: ''
  })
  handleSearch()
}

// 新增
const handleAdd = () => {
  dialogTitle.value = '新增用户'
  Object.assign(form, {
    id: null,
    name: '',
    email: '',
    phone: '',
    role: 'user'
  })
  dialogVisible.value = true
}

// 编辑
const handleEdit = (row) => {
  dialogTitle.value = '编辑用户'
  Object.assign(form, { ...row })
  dialogVisible.value = true
}

// 删除
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除用户"${row.name}"吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 模拟删除操作
    ElMessage.success('删除成功')
    getTableData()
  } catch {
    // 用户取消
  }
}

// 状态切换
const handleStatusChange = (row) => {
  ElMessage.success(`已${row.status ? '启用' : '禁用'}用户"${row.name}"`)
}

// 多选
const handleSelectionChange = (selection) => {
  selectedRows.value = selection
}

// 分页大小改变
const handleSizeChange = (size) => {
  pagination.pageSize = size
  getTableData()
}

// 当前页改变
const handleCurrentChange = (page) => {
  pagination.currentPage = page
  getTableData()
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid) => {
    if (valid) {
      submitLoading.value = true
      try {
        // 模拟提交
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        ElMessage.success(form.id ? '修改成功' : '新增成功')
        dialogVisible.value = false
        getTableData()
      } finally {
        submitLoading.value = false
      }
    }
  })
}

// 关闭弹窗
const handleDialogClose = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 页面加载
onMounted(() => {
  getTableData()
})
</script>

<style scoped>
.data-table-page {
  min-height: 100%;
}

.page-title {
  margin-bottom: 24px;
  color: #333;
  font-size: 24px;
  font-weight: 500;
}

.search-card {
  margin-bottom: 16px;
}

.table-card {
  margin-bottom: 16px;
}

.pagination-container {
  margin-top: 16px;
  text-align: right;
}

/* 深色模式 */
:global(.dark) .page-title {
  color: #fff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .data-table-page {
    padding: 0;
  }
  
  .page-title {
    font-size: 20px;
    margin-bottom: 16px;
  }
  
  .search-card {
    margin-bottom: 12px;
  }
  
  :deep(.el-card__body) {
    padding: 16px;
  }
  
  /* 搜索表单响应式 */
  .search-card :deep(.el-row) {
    flex-direction: column;
  }
  
  .search-card :deep(.el-col) {
    width: 100% !important;
    margin-bottom: 12px;
  }
  
  .search-card :deep(.el-col:last-child) {
    margin-bottom: 0;
  }
  
  .search-card :deep(.el-button) {
    width: 100%;
    margin-bottom: 8px;
    margin-left: 0 !important;
  }
  
  .search-card :deep(.el-button:last-child) {
    margin-bottom: 0;
  }
  
  /* 表格响应式 */
  .table-card {
    margin-bottom: 12px;
  }
  
  :deep(.el-table) {
    font-size: 12px;
  }
  
  :deep(.el-table th),
  :deep(.el-table td) {
    padding: 8px 4px;
  }
  
  :deep(.el-table .cell) {
    padding: 0 4px;
    line-height: 1.4;
  }
  
  /* 表格列宽调整 */
  :deep(.el-table-column--selection .cell) {
    padding: 0 2px;
  }
  
  /* 操作按钮响应式 */
  :deep(.el-button--small) {
    padding: 4px 6px;
    font-size: 11px;
  }
  
  /* 状态开关响应式 */
  :deep(.el-switch) {
    font-size: 12px;
  }
  
  /* 标签响应式 */
  :deep(.el-tag) {
    font-size: 11px;
    padding: 0 4px;
  }
  
  /* 分页响应式 */
  .pagination-container {
    text-align: center;
    margin-top: 12px;
    padding: 12px 8px;
  }
  
  :deep(.el-pagination) {
    justify-content: center;
    flex-wrap: wrap;
    --el-pagination-font-size: 12px;
    --el-pagination-button-width: 28px;
    --el-pagination-button-height: 28px;
  }
  
  :deep(.el-pagination .el-pagination__sizes) {
    order: -1;
    margin-bottom: 8px;
    width: 100%;
    text-align: center;
  }
  
  :deep(.el-pagination .el-pagination__total) {
    order: -2;
    margin-bottom: 4px;
    width: 100%;
    text-align: center;
  }
  
  :deep(.el-pagination .el-pagination__jump) {
    margin-left: 0;
    margin-top: 8px;
    width: 100%;
    text-align: center;
  }
}

/* 平板设备适配 */
@media (min-width: 769px) and (max-width: 1024px) {
  .page-title {
    font-size: 22px;
  }
  
  :deep(.el-card__body) {
    padding: 18px;
  }
  
  :deep(.el-table) {
    font-size: 13px;
  }
  
  .search-card :deep(.el-col) {
    margin-bottom: 8px;
  }
}

/* 超小屏幕优化 */
@media (max-width: 480px) {
  .page-title {
    font-size: 18px;
    margin-bottom: 12px;
  }
  
  :deep(.el-card__body) {
    padding: 12px;
  }
  
  :deep(.el-card__header) {
    padding: 12px;
  }
  
  .search-card :deep(.el-input) {
    font-size: 14px;
  }
  
  .search-card :deep(.el-select) {
    font-size: 14px;
  }
  
  .search-card :deep(.el-button) {
    padding: 8px 12px;
    font-size: 12px;
  }
  
  :deep(.el-table) {
    font-size: 11px;
  }
  
  :deep(.el-table th),
  :deep(.el-table td) {
    padding: 6px 2px;
  }
  
  :deep(.el-button--small) {
    padding: 2px 4px;
    font-size: 10px;
  }
  
  :deep(.el-tag) {
    font-size: 10px;
    padding: 0 2px;
  }
  
  .pagination-container {
    padding: 8px 4px;
  }
  
  :deep(.el-pagination) {
    --el-pagination-button-width: 24px;
    --el-pagination-button-height: 24px;
    --el-pagination-font-size: 11px;
  }
}
</style>