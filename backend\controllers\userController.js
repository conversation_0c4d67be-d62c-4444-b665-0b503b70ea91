const { db, COLLECTIONS } = require('../config/database')
const Response = require('../utils/response')

class UserController {
  // 获取用户列表
  async getUsers(req, res) {
    try {
      const { page = 1, pageSize = 10, keyword = '', status = '' } = req.query
      
      const collection = db.collection(COLLECTIONS.USERS)
      let query = collection
      
      // 关键词搜索
      if (keyword) {
        query = query.where({
          $or: [
            { name: db.RegExp({ regexp: keyword, options: 'i' }) },
            { email: db.RegExp({ regexp: keyword, options: 'i' }) }
          ]
        })
      }
      
      // 状态筛选
      if (status !== '') {
        query = query.where({ status: parseInt(status) })
      }
      
      // 分页查询
      const skip = (page - 1) * pageSize
      const result = await query.skip(skip).limit(parseInt(pageSize)).get()
      const countResult = await query.count()
      
      res.json(Response.paginate(
        result.data,
        countResult.total,
        page,
        pageSize
      ))
    } catch (error) {
      console.error('获取用户列表失败:', error)
      res.status(500).json(Response.error('获取用户列表失败'))
    }
  }

  // 创建用户
  async createUser(req, res) {
    try {
      const { name, email, phone, role = 'user' } = req.body
      
      // 检查邮箱是否已存在
      const existUser = await db.collection(COLLECTIONS.USERS)
        .where({ email })
        .get()
      
      if (existUser.data.length > 0) {
        return res.status(400).json(Response.error('邮箱已存在'))
      }
      
      // 创建用户
      const userData = {
        name,
        email,
        phone,
        role,
        status: 1,
        createTime: new Date(),
        updateTime: new Date()
      }
      
      const result = await db.collection(COLLECTIONS.USERS).add(userData)
      
      res.json(Response.success({ id: result.id }, '用户创建成功'))
    } catch (error) {
      console.error('创建用户失败:', error)
      res.status(500).json(Response.error('创建用户失败'))
    }
  }

  // 更新用户
  async updateUser(req, res) {
    try {
      const { id } = req.params
      const { name, email, phone, role } = req.body
      
      const updateData = {
        name,
        email,
        phone,
        role,
        updateTime: new Date()
      }
      
      await db.collection(COLLECTIONS.USERS).doc(id).update(updateData)
      
      res.json(Response.success(null, '用户更新成功'))
    } catch (error) {
      console.error('更新用户失败:', error)
      res.status(500).json(Response.error('更新用户失败'))
    }
  }

  // 删除用户
  async deleteUser(req, res) {
    try {
      const { id } = req.params
      
      await db.collection(COLLECTIONS.USERS).doc(id).remove()
      
      res.json(Response.success(null, '用户删除成功'))
    } catch (error) {
      console.error('删除用户失败:', error)
      res.status(500).json(Response.error('删除用户失败'))
    }
  }

  // 更新用户状态
  async updateUserStatus(req, res) {
    try {
      const { id } = req.params
      const { status } = req.body
      
      await db.collection(COLLECTIONS.USERS).doc(id).update({
        status,
        updateTime: new Date()
      })
      
      res.json(Response.success(null, '状态更新成功'))
    } catch (error) {
      console.error('更新用户状态失败:', error)
      res.status(500).json(Response.error('更新用户状态失败'))
    }
  }

  // 获取用户统计信息
  async getUserStats(req, res) {
    try {
      const collection = db.collection(COLLECTIONS.USERS)
      
      // 总用户数
      const totalUsers = await collection.count()
      
      // 今日新增用户
      const today = new Date()
      today.setHours(0, 0, 0, 0)
      const todayUsers = await collection.where({
        createTime: db.command.gte(today)
      }).count()
      
      // 活跃用户数
      const activeUsers = await collection.where({
        status: 1
      }).count()
      
      // 各角色用户统计
      const roleStats = await collection.aggregate()
        .group({
          _id: '$role',
          count: db.command.aggregate.sum(1)
        })
        .end()
      
      const stats = {
        totalUsers: totalUsers.total,
        todayUsers: todayUsers.total,
        activeUsers: activeUsers.total,
        roleStats: roleStats.data
      }
      
      res.json(Response.success(stats))
    } catch (error) {
      console.error('获取用户统计失败:', error)
      res.status(500).json(Response.error('获取用户统计失败'))
    }
  }
}

module.exports = new UserController()