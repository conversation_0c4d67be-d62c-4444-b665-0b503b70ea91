<template>
  <div class="sidebar-container" :class="{ 
    'mobile-sidebar': isMobile,
    'sidebar-hidden': isMobile && isCollapse 
  }">
    <el-aside 
      :width="isCollapse ? '64px' : '240px'" 
      class="sidebar"
      :class="{ 'mobile-drawer': isMobile }"
    >
      <!-- Logo区域 -->
      <div class="logo-section" :class="{ 'logo-collapsed': isCollapse }">
        <div class="logo-wrapper">
          <div class="logo-icon">
            <el-icon size="32">
              <Monitor />
            </el-icon>
          </div>
          <transition name="logo-text">
            <div v-show="!isCollapse" class="logo-content">
              <h1 class="logo-title">项目管理</h1>
              <p class="logo-subtitle">Management System</p>
            </div>
          </transition>
        </div>
      </div>

      <!-- 用户信息区域 -->
      <transition name="user-info">
        <div v-show="false" class="user-info-section">
          <el-avatar :size="40" :src="userInfo.avatar" class="user-avatar">
            <el-icon><User /></el-icon>
          </el-avatar>
          <div class="user-details">
            <p class="user-name">{{ userInfo.name }}</p>
            <p class="user-role">{{ userInfo.role === 'admin' ? '管理员' : '用户' }}</p>
          </div>
          <el-dropdown trigger="click" class="user-dropdown">
            <el-button type="text" size="small" class="dropdown-trigger">
              <el-icon><MoreFilled /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item>
                  <el-icon><User /></el-icon>
                  个人资料
                </el-dropdown-item>
                <el-dropdown-item>
                  <el-icon><Setting /></el-icon>
                  系统设置
                </el-dropdown-item>
                <el-dropdown-item divided>
                  <el-icon><SwitchButton /></el-icon>
                  退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </transition>

      <!-- 菜单区域 -->
      <nav class="navigation-menu">
        <el-scrollbar height="100%">
          <el-menu
            :default-active="$route.path"
            :collapse="isCollapse"
            :unique-opened="true"
            router
            class="sidebar-menu"
          >
            <template v-for="route in menuRoutes" :key="route.path">
              <el-menu-item 
                :index="route.path"
                class="custom-menu-item"
              >
                <div class="menu-item-content">
                  <div class="menu-icon-wrapper">
                    <el-icon class="menu-icon">
                      <component :is="route.meta.icon" />
                    </el-icon>
                  </div>
                  <span class="menu-title">{{ route.meta.title }}</span>
                </div>
              </el-menu-item>
            </template>
          </el-menu>
        </el-scrollbar>
      </nav>

      <!-- 底部操作区域 -->
      <div class="sidebar-footer">
        <el-tooltip 
          :content="isCollapse ? '展开菜单' : '折叠菜单'" 
          placement="right"
          :disabled="!isCollapse"
        >
          <el-button 
            class="collapse-btn"
            :class="{ 'collapsed': isCollapse }"
            @click="toggleCollapse"
            circle
          >
            <el-icon class="collapse-icon">
              <Expand v-if="isCollapse" />
              <Fold v-else />
            </el-icon>
          </el-button>
        </el-tooltip>

        <transition name="theme-toggle">
          <el-tooltip 
            v-show="!isCollapse"
            :content="isDark ? '切换到亮色模式' : '切换到深色模式'" 
            placement="top"
          >
            <el-button 
              class="theme-btn"
              @click="toggleTheme"
              circle
            >
              <el-icon>
                <Sunny v-if="isDark" />
                <Moon v-else />
              </el-icon>
            </el-button>
          </el-tooltip>
        </transition>
      </div>
    </el-aside>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { useLayoutStore, useUserStore } from '../../stores'

// 接收props
const props = defineProps({
  isMobile: {
    type: Boolean,
    default: false
  }
})

const router = useRouter()
const layoutStore = useLayoutStore()
const userStore = useUserStore()

// 状态管理
const isCollapse = computed(() => layoutStore.isCollapse)
const isDark = computed(() => layoutStore.isDark)
const userInfo = computed(() => userStore.userInfo)
const isMobile = computed(() => props.isMobile)

// 操作方法
const toggleCollapse = () => layoutStore.toggleCollapse()
const toggleTheme = () => layoutStore.toggleTheme()

// 菜单路由（过滤掉重定向）
const menuRoutes = computed(() => {
  return router.getRoutes().filter(route => 
    route.path !== '/' && route.meta?.title
  )
})
</script>

<style scoped>
/* ===== 容器和基础布局 ===== */
.sidebar-container {
  height: 100%;
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  z-index: 1001;
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.sidebar {
  height: 100vh;
  background: linear-gradient(180deg, 
    #667eea 0%, 
    #764ba2 50%, 
    #4c63d2 100%);
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: relative;
}

.sidebar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  pointer-events: none;
}

/* ===== Logo区域 ===== */
.logo-section {
  padding: 20px 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
}

.logo-section.logo-collapsed {
  padding: 20px 12px;
}

.logo-wrapper {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.logo-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  flex-shrink: 0;
  transition: all 0.3s ease;
}

.logo-icon:hover {
  transform: scale(1.05);
  background: rgba(255, 255, 255, 0.3);
}

.logo-icon .el-icon {
  color: #fff;
  font-weight: bold;
}

.logo-content {
  margin-left: 16px;
  color: #fff;
}

.logo-title {
  font-size: 20px;
  font-weight: 700;
  margin: 0;
  line-height: 1.2;
  letter-spacing: 0.5px;
  white-space: nowrap;
  overflow: hidden;
}

.logo-subtitle {
  font-size: 11px;
  opacity: 0.8;
  margin: 2px 0 0 0;
  font-weight: 400;
  text-transform: uppercase;
  letter-spacing: 1px;
  white-space: nowrap;
  overflow: hidden;
}

/* Logo动画 */
.logo-text-enter-active, .logo-text-leave-active {
  transition: all 0.3s ease;
}

.logo-text-enter-from, .logo-text-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}

/* ===== 用户信息区域 ===== */
.user-info-section {
  padding: 16px;
  margin: 8px 12px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  gap: 12px;
  position: relative;
  z-index: 1;
}

.user-avatar {
  border: 2px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

.user-avatar:hover {
  transform: scale(1.1);
  border-color: rgba(255, 255, 255, 0.6);
}

.user-details {
  flex: 1;
  min-width: 0;
}

.user-name {
  font-size: 14px;
  font-weight: 600;
  color: #fff;
  margin: 0;
  line-height: 1.2;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-role {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  margin: 2px 0 0 0;
  line-height: 1;
}

.user-dropdown .dropdown-trigger {
  color: rgba(255, 255, 255, 0.8);
  border: none;
  background: transparent;
  padding: 4px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.user-dropdown .dropdown-trigger:hover {
  background: rgba(255, 255, 255, 0.2);
  color: #fff;
}

/* 用户信息动画 */
.user-info-enter-active, .user-info-leave-active {
  transition: all 0.3s ease;
}

.user-info-enter-from, .user-info-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* ===== 导航菜单区域 ===== */
.navigation-menu {
  flex: 1;
  padding: 8px 0;
  position: relative;
  z-index: 1;
  overflow: hidden;
}

.sidebar-menu {
  background: transparent !important;
  border: none !important;
  padding: 0 12px;
}

.custom-menu-item {
  margin: 4px 0;
  margin-left: -5px;
  border-radius: 10px !important;
  background: transparent !important;
  border: none !important;
  padding: 0 8px !important;
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.menu-item-content {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-radius: 10px;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  position: relative;
  overflow: hidden;
  cursor: pointer;
  width: 100%;
}
.menu-icon{
  margin-left: 5px;
}

/* 折叠状态下的样式 - 参考logo-icon的设计 */
:deep(.el-menu--collapse) .custom-menu-item {
  margin: 8px 0 8px -3px !important;
  padding: 0 !important;
  width: 48px !important;
  display: flex !important;
  justify-content: center !important;
}

:deep(.el-menu--collapse) .menu-item-content {
  padding: 0 !important;
  width: 44px !important;
  height: 44px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  border-radius: 10px !important;
  background: rgba(255, 255, 255, 0.08) !important;
  backdrop-filter: blur(8px) !important;
  border: 1px solid rgba(255, 255, 255, 0.15) !important;
  transition: all 0.3s ease !important;
}

:deep(.el-menu--collapse) .menu-icon-wrapper {
  width: auto !important;
  height: auto !important;
  background: transparent !important;
  border-radius: 0 !important;
  margin: 0 !important;
}

:deep(.el-menu--collapse) .menu-icon {
  margin: 0 !important;
  font-size: 18px !important;
}

:deep(.el-menu--collapse) .menu-title {
  display: none !important;
}

/* 折叠状态下的悬停效果 - 稍微不同的样式 */
:deep(.el-menu--collapse) .custom-menu-item:hover .menu-item-content {
  transform: translateY(-2px) scale(1.02) !important;
  background: rgba(255, 255, 255, 0.15) !important;
  border-color: rgba(255, 255, 255, 0.3) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  
}

/* 折叠状态下的激活效果 */
:deep(.el-menu--collapse) .el-menu-item.is-active {
  transform: none !important;
  background: transparent !important;
  box-shadow: none !important;
  margin: 8px 0 8px -6px !important;
  width: 48px !important;
}

:deep(.el-menu--collapse) .el-menu-item.is-active .menu-item-content {
  background: rgba(255, 255, 255, 0.2) !important;
  border-color: rgba(255, 255, 255, 0.4) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.12) !important;
}

/* 悬停状态移到父级元素，保持与激活状态一致 - 只在展开状态下生效 */
.sidebar-menu:not(.el-menu--collapse) .custom-menu-item:hover {
  background: rgba(255, 255, 255, 0.15) !important;
  transform: translateX(4px);
  border-radius: 10px !important;
}

.sidebar-menu:not(.el-menu--collapse) .custom-menu-item:hover .menu-item-content {
  background: transparent !important;
}

.sidebar-menu:not(.el-menu--collapse) .custom-menu-item:hover .menu-icon-wrapper {
  transform: scale(1.1);
  background: rgba(255, 255, 255, 0.3);
}

.menu-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  margin-right: 12px;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.menu-icon {
  color: #fff !important;
  font-size: 18px;
}

.menu-title {
  color: #fff !important;
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
  flex: 1;
}



/* 激活状态 */
:deep(.el-menu-item.is-active) {
  background: rgba(255, 255, 255, 0.25) !important;
  border-radius: 10px !important;
  transform: translateX(4px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  width: 100%;
}

:deep(.el-menu-item.is-active .menu-item-content) {
  background: transparent !important;
}



:deep(.el-menu-item.is-active .menu-icon-wrapper) {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

/* ===== 底部操作区域 ===== */
.sidebar-footer {
  padding: 16px 12px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 8px;
  position: relative;
  z-index: 1;
}

.collapse-btn, .theme-btn {
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  color: #fff !important;
  transition: all 0.3s ease !important;
  backdrop-filter: blur(10px);
}

.collapse-btn:hover, .theme-btn:hover {
  background: rgba(255, 255, 255, 0.2) !important;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.collapse-btn.collapsed {
  margin: 0 auto;
}

.collapse-icon {
  transition: transform 0.3s ease;
}

.collapse-btn:hover .collapse-icon {
  transform: scale(1.1);
}

/* 主题切换动画 */
.theme-toggle-enter-active, .theme-toggle-leave-active {
  transition: all 0.3s ease;
}

.theme-toggle-enter-from, .theme-toggle-leave-to {
  opacity: 0;
  transform: scale(0.8);
}

/* ===== 深色模式适配 ===== */
:global(.dark) .sidebar {
  background: linear-gradient(180deg, 
    #1a1a2e 0%, 
    #16213e 50%, 
    #0f1419 100%);
}

:global(.dark) .sidebar::before {
  background: rgba(0, 0, 0, 0.3);
}

:global(.dark) .logo-icon {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
}

:global(.dark) .user-info-section {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
}

:global(.dark) .menu-icon-wrapper {
  background: rgba(255, 255, 255, 0.05);
}

:global(.dark) .menu-item-content:hover {
  background: rgba(255, 255, 255, 0.08);
}

:global(.dark) .collapse-btn, 
:global(.dark) .theme-btn {
  background: rgba(255, 255, 255, 0.05) !important;
  border-color: rgba(255, 255, 255, 0.1) !important;
}

/* ===== 移动端抽屉式导航 ===== */
.mobile-sidebar {
  transform: translateX(0);
  transition: transform 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.sidebar-hidden {
  transform: translateX(-100%);
}

.mobile-drawer {
  width: 280px !important;
  box-shadow: 4px 0 20px rgba(0, 0, 0, 0.25) !important;
}

/* ===== 响应式设计 ===== */
@media (max-width: 768px) {
  .sidebar-container {
    z-index: 2000;
    position: fixed !important;
    left: 0;
    top: 0;
    bottom: 0;
    width: 280px;
    transform: translateX(-100%);
    transition: transform 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  }
  
  .sidebar-container.mobile-sidebar:not(.sidebar-hidden) {
    transform: translateX(0);
  }
  
  .sidebar {
    width: 280px !important;
    box-shadow: 4px 0 20px rgba(0, 0, 0, 0.25);
  }
  
  /* 移动端菜单项优化 */
  .custom-menu-item {
    margin: 6px 0;
  }
  
  .menu-item-content {
    padding: 14px 16px;
  }
  
  .menu-title {
    font-size: 15px;
  }
  
  /* 移动端logo区域 */
  .logo-section {
    padding: 24px 16px;
  }
  
  .logo-title {
    font-size: 22px;
  }
  
  /* 移动端底部操作区域 */
  .sidebar-footer {
    padding: 20px 16px;
  }
}

/* 平板设备适配 */
@media (min-width: 769px) and (max-width: 1024px) {
  .sidebar-container {
    position: fixed;
    z-index: 1500;
  }
  
  .sidebar {
    box-shadow: 2px 0 12px rgba(0, 0, 0, 0.1);
  }
}

/* 超小屏幕优化 */
@media (max-width: 480px) {
  .sidebar-container {
    width: 260px;
  }
  
  .mobile-drawer {
    width: 260px !important;
  }
  
  .sidebar {
    width: 260px !important;
  }
  
  .logo-section {
    padding: 20px 12px;
  }
  
  .logo-title {
    font-size: 20px;
  }
  
  .logo-subtitle {
    font-size: 10px;
  }
  
  .menu-item-content {
    padding: 12px 14px;
  }
  
  .menu-title {
    font-size: 14px;
  }
}

/* ===== 滚动条美化 ===== */
:deep(.el-scrollbar__bar) {
  opacity: 0.3;
}

:deep(.el-scrollbar__thumb) {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
}

:deep(.el-scrollbar__thumb:hover) {
  background: rgba(255, 255, 255, 0.5);
}
</style>