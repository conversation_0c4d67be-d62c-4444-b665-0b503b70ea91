import request from '../utils/request'

// 获取用户的供应信息列表
export const getUserSupplies = (openid) => {
  return request({
    url: `/api/supply/user/${openid}`,
    method: 'get'
  })
}

// 更新供应信息
export const updateSupply = (id, data) => {
  return request({
    url: `/api/supply/${id}`,
    method: 'put',
    data
  })
}

// 删除供应信息
export const deleteSupply = (id) => {
  return request({
    url: `/api/supply/${id}`,
    method: 'delete'
  })
}