<template>
  <div class="layout-container">
    <!-- 移动端遮罩层 -->
    <div 
      v-if="isMobile && !isCollapse" 
      class="mobile-overlay"
      @click="handleMobileOverlayClick"
    ></div>
    
    <!-- 侧边栏 -->
    <Sidebar :is-mobile="isMobile" />
    
    <!-- 主内容区域 -->
    <div class="main-container" :class="{ 
      'is-collapse': isCollapse,
      'is-mobile': isMobile 
    }">
      <!-- 顶部导航 -->
      <Header :is-mobile="isMobile" />
      
      <!-- 页面内容 -->
      <el-main class="page-main">
        <div class="content-wrapper">
          <router-view v-slot="{ Component, route }">
            <transition name="fade-slide" mode="out-in">
              <component :is="Component" :key="route.path" />
            </transition>
          </router-view>
        </div>
      </el-main>
    </div>
  </div>
</template>

<script setup>
import { computed, ref, onMounted, onUnmounted } from 'vue'
import { useLayoutStore } from '../../stores'
import Sidebar from './Sidebar.vue'
import Header from './Header.vue'

const layoutStore = useLayoutStore()
const isCollapse = computed(() => layoutStore.isCollapse)

// 移动端检测
const isMobile = ref(false)

const checkIsMobile = () => {
  isMobile.value = window.innerWidth <= 768
  // 移动端默认折叠侧边栏
  if (isMobile.value && !isCollapse.value) {
    layoutStore.setCollapse(true)
  }
}

const handleResize = () => {
  checkIsMobile()
}

// 移动端遮罩层点击处理
const handleMobileOverlayClick = () => {
  if (isMobile.value) {
    layoutStore.setCollapse(true)
  }
}

onMounted(() => {
  checkIsMobile()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.layout-container {
  height: 100vh;
  width: 100%;
  display: flex;
}

.main-container {
  flex: 1;
  min-height: 100vh;
  transition: margin-left 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
  background-color: #f0f2f5;
  margin-left: 240px;
  overflow: auto;
  min-width: 0;
}

.main-container.is-collapse {
  margin-left: 64px;
}

.page-main {
  padding: 0 !important;
  min-height: calc(100vh - 60px);
  margin: 0;
}

.content-wrapper {
  background-color: #fff;
  border-radius: 0;
  padding: 24px;
  box-shadow: none;
  min-height: calc(100vh - 60px);
  margin: 0;
  width: 100%;
  height: 100%;
}

/* 覆盖Element Plus的默认样式 */
:deep(.el-main) {
  padding: 0 !important;
  margin: 0;
}

/* 页面切换动画 */
.fade-slide-enter-active,
.fade-slide-leave-active {
  transition: all 0.3s ease;
}

.fade-slide-enter-from {
  opacity: 0;
  transform: translateX(30px);
}

.fade-slide-leave-to {
  opacity: 0;
  transform: translateX(-30px);
}

/* 移动端遮罩层 */
.mobile-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1999;
  transition: opacity 0.3s ease;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-container {
    margin-left: 0 !important;
    width: 100%;
  }
  
  .main-container.is-collapse {
    margin-left: 0 !important;
  }
  
  .main-container.is-mobile {
    margin-left: 0 !important;
  }
  
  .content-wrapper {
    padding: 16px !important;
    border-radius: 0;
  }
  
  .page-main {
    min-height: calc(100vh - 60px);
  }
}

/* 平板设备 */
@media (min-width: 769px) and (max-width: 1024px) {
  .content-wrapper {
    padding: 20px;
  }
}

/* 超小屏幕 */
@media (max-width: 480px) {
  .content-wrapper {
    padding: 12px !important;
  }
}

/* 深色模式 */
:global(.dark) .main-container {
  background-color: #000;
}

:global(.dark) .content-wrapper {
  background-color: #141414;
  border: 1px solid #303030;
}
</style>