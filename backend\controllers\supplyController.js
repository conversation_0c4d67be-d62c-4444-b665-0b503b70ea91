const { db } = require('../config/database')
const Response = require('../utils/response')

class SupplyController {
  // 获取用户的供应信息列表
  async getUserSupplies(req, res) {
    try {
      const { openid } = req.params
      
      if (!openid) {
        return res.status(400).json(Response.error('用户openid不能为空'))
      }
      
      console.log(`开始查询用户 ${openid} 的供应信息...`)
      
      // 查询该用户的所有供应信息
      const result = await Promise.race([
        db.collection('supply_content')
          .where({ _openid: openid })
          .orderBy('createTime', 'desc')
          .get(),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('查询超时')), 15000)
        )
      ])
      
      console.log(`查询成功，找到 ${result.data.length} 条供应信息`)
      
      res.json(Response.success({
        supplies: result.data,
        total: result.data.length
      }))
    } catch (error) {
      console.error('获取用户供应信息失败:', error)
      res.status(500).json(Response.error('获取供应信息失败'))
    }
  }
  
  // 更新供应信息
  async updateSupply(req, res) {
    try {
      const { id } = req.params
      const updateData = req.body
      
      if (!id) {
        return res.status(400).json(Response.error('供应信息ID不能为空'))
      }
      
      // 移除不需要更新的字段
      delete updateData._id
      delete updateData._openid
      
      // 添加更新时间
      updateData.updateTime = new Date()
      
      console.log(`开始更新供应信息 ${id}...`)
      
      const result = await db.collection('supply_content')
        .doc(id)
        .update(updateData)
      
      console.log('供应信息更新成功')
      
      res.json(Response.success(result, '供应信息更新成功'))
    } catch (error) {
      console.error('更新供应信息失败:', error)
      res.status(500).json(Response.error('更新供应信息失败'))
    }
  }
  
  // 删除供应信息
  async deleteSupply(req, res) {
    try {
      const { id } = req.params
      
      if (!id) {
        return res.status(400).json(Response.error('供应信息ID不能为空'))
      }
      
      console.log(`开始删除供应信息 ${id}...`)
      
      const result = await db.collection('supply_content')
        .doc(id)
        .remove()
      
      console.log('供应信息删除成功')
      
      res.json(Response.success(result, '供应信息删除成功'))
    } catch (error) {
      console.error('删除供应信息失败:', error)
      res.status(500).json(Response.error('删除供应信息失败'))
    }
  }
}

module.exports = new SupplyController()