import { createRouter, createWebHistory } from 'vue-router'

const routes = [
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/dashboard',
    component: () => import('../views/Dashboard.vue'),
    meta: { title: '仪表盘', icon: 'Monitor' }
  },
  {
    path: '/data-table',
    component: () => import('../views/DataTable.vue'),
    meta: { title: '数据管理', icon: 'Grid' }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router