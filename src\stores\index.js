import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

// 用户信息store
export const useUserStore = defineStore('user', () => {
  const userInfo = ref({
    name: '管理员',
    avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
    role: 'admin'
  })

  const isAdmin = computed(() => userInfo.value.role === 'admin')

  function updateUserInfo(info) {
    userInfo.value = { ...userInfo.value, ...info }
  }

  return {
    userInfo,
    isAdmin,
    updateUserInfo
  }
})

// 布局状态store
export const useLayoutStore = defineStore('layout', () => {
  const isCollapse = ref(false)
  const isDark = ref(false)

  function toggleCollapse() {
    isCollapse.value = !isCollapse.value
  }

  function setCollapse(value) {
    isCollapse.value = value
  }

  function toggleTheme() {
    isDark.value = !isDark.value
    // 切换主题时更新HTML类名
    document.documentElement.classList.toggle('dark', isDark.value)
  }

  return {
    isCollapse,
    isDark,
    toggleCollapse,
    setCollapse,
    toggleTheme
  }
})