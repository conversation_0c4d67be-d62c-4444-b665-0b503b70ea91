import request from '../utils/request'

// 获取用户列表
export const getUserList = (params) => {
  return request.get('/api/users', { params })
}

// 创建用户
export const createUser = (data) => {
  return request.post('/api/users', data)
}

// 更新用户
export const updateUser = (id, data) => {
  return request.put(`/api/users/${id}`, data)
}

// 删除用户
export const deleteUser = (id) => {
  return request.delete(`/api/users/${id}`)
}

// 更新用户状态
export const updateUserStatus = (id, status) => {
  return request.patch(`/api/users/${id}/status`, { status })
}

// 获取用户统计
export const getUserStats = () => {
  return request.get('/api/users/stats')
}